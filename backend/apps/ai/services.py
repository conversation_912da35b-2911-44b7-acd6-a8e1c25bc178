"""
AI服务核心模块 - 灵境(Mentia)智能助手核心服务

文件功能：
    提供基于大语言模型的各种AI功能服务，包括项目管理、内容生成、技能分析等核心功能

主要功能模块：
    1. 智能模型选择机制 - 根据任务复杂度和性价比动态选择最适合的AI模型
    2. 项目子任务生成 - 将大目标分解为可执行的具体任务
    3. 标题摘要生成 - 为博客、日记等内容自动生成标题和摘要
    4. 项目复盘报告生成 - 基于项目数据生成深度复盘分析
    5. 技能标签提取 - 从项目经验中识别和提取技能标签
    6. 向量嵌入服务 - 为内容检索和相似度匹配提供向量化支持

技术架构：
    - 支持多模型配置：qwen-flash(高性价比)、qwen-plus(平衡)、qwen-max(高质量)
    - 基于OpenAI兼容API接口，支持阿里云通义千问等第三方服务
    - 采用异步处理和错误恢复机制，确保服务稳定性
    - 集成缓存机制，优化API调用性能

作者: Mentia开发团队
创建时间: 2024
最后更新: 2025-09-05
版本: v2.0 - 新增智能模型选择机制
"""
from openai import OpenAI
import hashlib
import json
import logging
from typing import List, Dict, Any, Optional
from django.conf import settings
from django.core.cache import cache
from .models import VectorEmbedding
from apps.growth.models import GrowthItem
from apps.journal.models import JournalEntry

logger = logging.getLogger(__name__)


class AIService:
    """
    AI服务核心类 - 灵境智能助手的核心服务提供者

    功能概述：
        这是整个AI功能的核心服务类，负责管理与大语言模型的交互，
        提供智能化的内容生成、分析和处理服务。

    核心特性：
        1. 智能模型选择：根据任务复杂度自动选择最适合的AI模型
        2. 多场景支持：支持项目管理、内容创作、技能分析等多种场景
        3. 性能优化：集成缓存机制，减少重复API调用
        4. 错误处理：完善的异常处理和降级策略

    模型配置：
        - flash_model: 高性价比模型，适用于简单任务（如标题生成、技能提取）
        - plus_model: 平衡型模型，适用于中等复杂度任务（如聊天、子任务生成）
        - max_model: 高质量模型，适用于复杂任务（如深度复盘分析）

    使用示例：
        ai_service = AIService()
        subtasks = await ai_service.generate_subtasks(project_data)
        review = await ai_service.generate_project_review(project_data)

    TODO:
        - 添加模型性能监控和自动切换机制
        - 实现更细粒度的成本控制策略
        - 支持自定义提示词模板管理
    """

    def __init__(self):
        """
        初始化AI服务实例

        设置各种模型配置和客户端连接参数。
        采用延迟初始化策略，只有在实际使用时才建立API连接。
        """
        self.client = None  # OpenAI客户端实例，延迟初始化
        self.embedding_model = settings.EMBEDDING_MODEL  # 向量嵌入模型
        self.chat_model = settings.CHAT_MODEL  # 默认聊天模型

        # 多模型配置 - 根据任务复杂度智能选择
        self.flash_model = getattr(settings, 'FLASH_MODEL', 'qwen-flash')  # 高性价比模型
        self.plus_model = getattr(settings, 'PLUS_MODEL', 'qwen-plus')    # 平衡型模型
        self.max_model = getattr(settings, 'MAX_MODEL', 'qwen-max')       # 高质量模型

    def _get_client(self):
        """
        延迟初始化OpenAI客户端（支持阿里云通义千问等第三方API）

        功能说明：
            采用延迟初始化策略，只有在实际需要调用AI服务时才建立连接。
            支持多种API提供商，包括OpenAI官方API和阿里云通义千问API。

        连接逻辑：
            1. 检查是否已有客户端实例，避免重复初始化
            2. 优先使用配置的BASE_URL（支持第三方API）
            3. 如果没有BASE_URL，则使用OpenAI官方API
            4. 异常处理确保服务稳定性

        Returns:
            OpenAI: 初始化的客户端实例，失败时返回None

        TODO: 添加连接池管理和自动重连机制
        """
        if self.client is None and settings.OPENAI_API_KEY:
            try:
                # 支持阿里云Qwen API和其他第三方API
                base_url = getattr(settings, 'BASE_URL', None)
                if base_url:
                    self.client = OpenAI(
                        api_key=settings.OPENAI_API_KEY,
                        base_url=base_url
                    )
                    logger.info(f"Initialized AI client with custom base URL: {base_url}")
                else:
                    # 默认OpenAI官方API
                    self.client = OpenAI(api_key=settings.OPENAI_API_KEY)
                    logger.info("Initialized AI client with OpenAI official API")
            except Exception as e:
                logger.error(f"Failed to initialize OpenAI client: {e}")
                return None
        return self.client

    def _select_model_by_complexity(self, task_type: str, content_length: int = 0, max_tokens: int = 500) -> str:
        """
        智能模型选择算法 - 根据任务复杂度和性价比动态选择最适合的AI模型

        设计理念：
            平衡模型性能、响应速度和成本，为不同类型的任务选择最优模型。
            遵循"够用即可"的原则，避免过度使用高成本模型。

        选择策略：
            1. 简单任务（标题生成、技能提取）→ Flash模型（高性价比）
            2. 中等任务（聊天对话、子任务生成）→ Plus模型（性能平衡）
            3. 复杂任务（深度复盘分析）→ Max模型（高质量输出）

        判断维度：
            - task_type: 任务类型，决定基础复杂度
            - content_length: 输入内容长度，影响处理难度
            - max_tokens: 预期输出长度，影响生成质量要求

        Args:
            task_type (str): 任务类型标识
                - 'title_summary': 标题摘要生成（简单）
                - 'skill_extraction': 技能标签提取（简单）
                - 'chat': AI伴侣对话（中等）
                - 'subtask': 项目子任务生成（中等）
                - 'review': 项目复盘分析（复杂）
            content_length (int): 输入内容字符长度，默认0
            max_tokens (int): 预期输出token数量，默认500

        Returns:
            str: 选择的模型名称（flash_model/plus_model/max_model）

        TODO:
            - 添加模型性能统计，优化选择算法
            - 支持用户自定义模型偏好设置
            - 实现基于历史表现的动态调整
        """
        # 简单任务策略：使用flash模型（高性价比）
        # 适用场景：标题生成、技能提取等结构化输出任务
        if task_type in ['title_summary', 'skill_extraction'] and content_length < 1000:
            logger.debug(f"Selected flash model for simple task: {task_type}")
            return self.flash_model

        # 中等复杂度策略：使用plus模型（平衡性能和成本）
        # 适用场景：日常对话、任务分解等需要一定理解能力的任务
        if task_type in ['subtask', 'chat'] and max_tokens <= 800:
            logger.debug(f"Selected plus model for medium task: {task_type}")
            return self.plus_model

        # 复杂任务策略：使用max模型（高质量输出）
        # 适用场景：深度分析、长文本处理等需要高质量输出的任务
        if task_type in ['review'] or content_length > 2000 or max_tokens > 800:
            logger.debug(f"Selected max model for complex task: {task_type}")
            return self.max_model

        # 默认策略：使用plus模型作为安全选择
        logger.debug(f"Selected default plus model for task: {task_type}")
        return self.plus_model
    
    async def generate_subtasks(self, project_title: str, project_description: str = "") -> List[Dict[str, str]]:
        """
        任务1.1: AI驱动的开始 - 生成子任务
        根据项目标题和描述，生成可执行的子任务清单
        """
        client = self._get_client()
        if not client:
            logger.error("OpenAI client not initialized. Please check OPENAI_API_KEY.")
            return self._get_fallback_subtasks(project_title)

        try:
            # 构建提示词
            prompt = self._build_subtask_prompt(project_title, project_description)

            # 调用OpenAI API
            client = self._get_client()
            if not client:
                raise Exception("AI服务未配置")

            # 智能选择模型
            selected_model = self._select_model_by_complexity('subtask', len(prompt), 1000)

            response = client.chat.completions.create(
                model=selected_model,
                messages=[
                    {"role": "system", "content": "你是一个专业的项目管理助手，擅长将大目标分解为可执行的小任务。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=1000
            )
            
            # 解析响应
            content = response.choices[0].message.content
            subtasks = self._parse_subtasks_response(content)
            
            logger.info(f"Generated {len(subtasks)} subtasks for project: {project_title}")
            return subtasks
            
        except Exception as e:
            logger.error(f"Error generating subtasks: {str(e)}")
            return self._get_fallback_subtasks(project_title)
    
    async def generate_title_and_summary(self, content: str, content_type: str = "blog") -> Dict[str, str]:
        """
        任务1.2: AI辅助标题与摘要
        根据内容生成标题和摘要
        """
        client = self._get_client()
        if not client:
            logger.error("OpenAI client not initialized. Please check OPENAI_API_KEY.")
            return {"title": "未命名", "summary": "暂无摘要"}

        try:
            # 构建提示词
            prompt = self._build_title_summary_prompt(content, content_type)

            # 调用OpenAI API
            client = self._get_client()
            if not client:
                raise Exception("AI服务未配置")

            # 智能选择模型 - 标题摘要是简单任务，使用flash模型
            selected_model = self._select_model_by_complexity('title_summary', len(content), 500)

            response = client.chat.completions.create(
                model=selected_model,
                messages=[
                    {"role": "system", "content": "你是一个专业的内容编辑，擅长为文章生成吸引人的标题和精准的摘要。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.8,
                max_tokens=500
            )
            
            # 解析响应
            content_response = response.choices[0].message.content
            result = self._parse_title_summary_response(content_response)
            
            logger.info(f"Generated title and summary for {content_type} content")
            return result
            
        except Exception as e:
            logger.error(f"Error generating title and summary: {str(e)}")
            return {"title": "未命名", "summary": "暂无摘要"}
    
    def _build_subtask_prompt(self, title: str, description: str) -> str:
        """构建子任务生成的提示词"""
        base_prompt = f"""
请基于以下项目信息生成具体可执行的子任务清单：

项目标题：{title}
项目描述：{description if description else "无具体描述"}

重要要求：
1. 严格基于项目标题和描述生成子任务，不要添加项目中没有提到的内容
2. 生成3-6个具体可执行的子任务（根据项目复杂度调整数量）
3. 每个子任务必须是明确的行动步骤，避免模糊表述
4. 子任务之间应该有合理的逻辑顺序
5. 子任务描述要简洁明确，适合实际执行
6. 不要编造具体的技术细节或工具，除非项目描述中明确提到

请严格按以下格式返回，不要添加额外说明：
1. [子任务标题] - [子任务描述]
2. [子任务标题] - [子任务描述]
...
"""
        return base_prompt.strip()
    
    def _build_title_summary_prompt(self, content: str, content_type: str) -> str:
        """构建标题摘要生成的提示词"""
        type_map = {
            "blog": "博客文章",
            "journal": "日记",
            "note": "笔记"
        }

        content_type_zh = type_map.get(content_type, "文章")

        # 限制内容长度并计算字数
        limited_content = content[:2000]
        content_length = len(content)

        # 根据内容长度确定摘要长度
        if content_length <= 200:
            summary_length = "30-50字"
        elif content_length <= 500:
            summary_length = "50-80字"
        elif content_length <= 1000:
            summary_length = "80-120字"
        else:
            summary_length = "120-200字"

        prompt = f"""
请仔细阅读以下{content_type_zh}内容，并基于实际内容生成标题和摘要：

内容：
{limited_content}

重要要求：
1. 标题必须准确反映文章的核心主题，不要添加文章中没有的内容
2. 摘要长度控制在{summary_length}，根据文章实际长度调整
3. 摘要必须基于文章实际内容，不要编造或推测文章中没有的信息
4. 如果文章内容较短，摘要也应相应简短
5. 保持语言自然流畅，符合中文表达习惯
6. 严格按照给定格式返回，不要添加额外说明

请按以下格式返回：
标题：[生成的标题]
摘要：[生成的摘要]
"""
        return prompt.strip()
    
    def _parse_subtasks_response(self, content: str) -> List[Dict[str, str]]:
        """解析子任务生成的响应"""
        subtasks = []
        lines = content.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or not any(line.startswith(str(i)) for i in range(1, 10)):
                continue
                
            # 移除序号
            line = line.split('.', 1)[-1].strip()
            
            # 分离标题和描述
            if ' - ' in line:
                title, description = line.split(' - ', 1)
                subtasks.append({
                    "title": title.strip(),
                    "description": description.strip()
                })
            else:
                subtasks.append({
                    "title": line,
                    "description": ""
                })
        
        return subtasks
    
    def _parse_title_summary_response(self, content: str) -> Dict[str, str]:
        """解析标题摘要生成的响应"""
        result = {"title": "未命名", "summary": "暂无摘要"}
        
        lines = content.strip().split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('标题：'):
                result["title"] = line.replace('标题：', '').strip()
            elif line.startswith('摘要：'):
                result["summary"] = line.replace('摘要：', '').strip()
        
        return result
    
    def _get_fallback_subtasks(self, title: str) -> List[Dict[str, str]]:
        """获取备用子任务（当AI调用失败时）"""
        return [
            {
                "title": "制定学习计划",
                "description": f"为「{title}」制定详细的学习计划和时间安排"
            },
            {
                "title": "收集学习资源",
                "description": "搜集相关的教程、文档、视频等学习材料"
            },
            {
                "title": "开始基础学习",
                "description": "从基础概念开始，逐步深入学习"
            },
            {
                "title": "实践练习",
                "description": "通过实际项目或练习来巩固所学知识"
            },
            {
                "title": "总结复盘",
                "description": "总结学习成果，记录经验和心得"
            }
        ]

    async def generate_project_review(self, project_data: dict) -> dict:
        """
        生成项目复盘报告

        Args:
            project_data: 项目数据，包含项目标题、描述和所有子任务信息

        Returns:
            dict: 包含复盘报告的字典
        """
        try:
            # 构建提示词
            prompt = self._build_project_review_prompt(project_data)

            # 调用OpenAI API
            client = self._get_client()
            if not client:
                raise Exception("AI服务未配置")

            # 智能选择模型 - 复盘生成是复杂任务，使用max模型确保高质量输出
            project_content_length = len(str(project_data))
            selected_model = self._select_model_by_complexity('review', project_content_length, 1500)

            response = client.chat.completions.create(
                model=selected_model,
                messages=[
                    {"role": "system", "content": "你是一个专业的项目管理顾问和成长导师，擅长帮助人们从完成的项目中提炼价值和洞察。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=1500
            )

            # 解析响应
            content_response = response.choices[0].message.content
            result = self._parse_project_review_response(content_response)

            logger.info(f"Generated project review for: {project_data.get('title', 'Unknown Project')}")
            return result

        except Exception as e:
            logger.error(f"Error generating project review: {str(e)}")
            return self._get_fallback_project_review(project_data)

    def _build_project_review_prompt(self, project_data: dict) -> str:
        """构建项目复盘的提示词"""
        title = project_data.get('title', '未知项目')
        description = project_data.get('description', '')
        subtasks = project_data.get('subtasks', [])

        subtasks_text = ""
        if subtasks:
            subtasks_text = "\n已完成的子任务：\n"
            for i, task in enumerate(subtasks, 1):
                subtasks_text += f"{i}. {task.get('title', '')} - {task.get('description', '')}\n"

        prompt = f"""
请基于以下已完成的项目信息，生成一份深度复盘报告：

项目标题：{title}
项目描述：{description if description else "无具体描述"}
{subtasks_text}

请从以下几个维度进行复盘分析：

1. 整体总结：用2-3句话概括这个项目的核心价值和意义
2. 关键成果：列出3-5个具体的成果或收获（要具体，不要泛泛而谈）
3. 技能提升：识别并列出通过这个项目获得或提升的3-5个技能
4. 改进建议：基于项目经验，提出3-4个未来可以改进的方向

要求：
- 基于实际项目内容进行分析，不要编造信息
- 语言要具体、有针对性，避免空泛的表述
- 重点关注个人成长和能力提升
- 保持积极正面的语调，同时客观指出改进空间

请严格按以下格式返回：
整体总结：[总结内容]

关键成果：
- [成果1]
- [成果2]
- [成果3]

技能提升：
- [技能1]
- [技能2]
- [技能3]

改进建议：
- [建议1]
- [建议2]
- [建议3]
"""
        return prompt.strip()

    def _parse_project_review_response(self, content: str) -> dict:
        """解析项目复盘响应"""
        result = {
            "review_summary": "项目已完成，正在生成复盘报告...",
            "key_achievements": [],
            "skills_gained": [],
            "improvement_areas": []
        }

        lines = content.strip().split('\n')
        current_section = None

        for line in lines:
            line = line.strip()
            if not line:
                continue

            if line.startswith('整体总结：'):
                result["review_summary"] = line.replace('整体总结：', '').strip()
            elif line.startswith('关键成果：'):
                current_section = 'achievements'
            elif line.startswith('技能提升：'):
                current_section = 'skills'
            elif line.startswith('改进建议：'):
                current_section = 'improvements'
            elif line.startswith('- '):
                item = line.replace('- ', '').strip()
                if current_section == 'achievements':
                    result["key_achievements"].append(item)
                elif current_section == 'skills':
                    result["skills_gained"].append(item)
                elif current_section == 'improvements':
                    result["improvement_areas"].append(item)

        return result

    def _get_fallback_project_review(self, project_data: dict) -> dict:
        """获取备用项目复盘（当AI调用失败时）"""
        title = project_data.get('title', '项目')
        return {
            "review_summary": f"恭喜完成「{title}」项目！这是一个重要的成长里程碑，通过系统性的任务执行，你在多个方面都有了提升。",
            "key_achievements": [
                f"成功完成「{title}」项目的所有关键任务",
                "建立了系统性的项目执行流程",
                "提升了任务管理和时间规划能力"
            ],
            "skills_gained": [
                "项目管理能力",
                "任务分解与执行能力",
                "持续学习能力"
            ],
            "improvement_areas": [
                "可以尝试设定更具挑战性的目标",
                "加强项目过程中的反思和调整",
                "考虑与他人协作完成更复杂的项目"
            ]
        }

    async def extract_skills_from_review(self, review_content: str, project_title: str = "") -> dict:
        """
        从复盘报告中提取技能标签

        Args:
            review_content: 复盘报告内容
            project_title: 项目标题（可选）

        Returns:
            dict: 包含提取的技能列表
        """
        try:
            # 构建提示词
            prompt = self._build_skill_extraction_prompt(review_content, project_title)

            # 调用OpenAI API
            client = self._get_client()
            if not client:
                raise Exception("AI服务未配置")

            # 智能选择模型 - 技能提取是简单任务，使用flash模型
            selected_model = self._select_model_by_complexity('skill_extraction', len(review_content), 800)

            response = client.chat.completions.create(
                model=selected_model,
                messages=[
                    {"role": "system", "content": "你是一个专业的技能分析师，擅长从项目经验中识别和提取具体的技能标签。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,  # 降低温度以获得更一致的结果
                max_tokens=800
            )

            # 解析响应
            content_response = response.choices[0].message.content
            result = self._parse_skill_extraction_response(content_response)

            logger.info(f"Extracted {len(result.get('skills', []))} skills from review")
            return result

        except Exception as e:
            logger.error(f"Error extracting skills: {str(e)}")
            return self._get_fallback_skills(project_title)

    def _build_skill_extraction_prompt(self, review_content: str, project_title: str) -> str:
        """构建技能提取的提示词"""
        prompt = f"""
请仔细分析以下项目复盘报告，提取其中涉及的具体技能标签：

项目标题：{project_title if project_title else "未知项目"}

复盘报告内容：
{review_content}

请从以下几个维度提取技能标签：
1. 技术技能：编程语言、框架、工具、平台等
2. 软技能：沟通、管理、分析、创新等能力
3. 领域知识：特定行业或领域的专业知识
4. 方法论：项目管理、设计思维、敏捷开发等

提取要求：
- 只提取报告中明确提到或暗示的技能
- 技能名称要具体、准确，避免过于宽泛
- 每个技能用2-4个字的简洁词汇表示
- 优先提取技术性和专业性较强的技能
- 总数控制在3-8个技能之间

请严格按以下格式返回，每行一个技能：
技能1
技能2
技能3
...
"""
        return prompt.strip()

    def _parse_skill_extraction_response(self, content: str) -> dict:
        """解析技能提取响应"""
        skills = []
        lines = content.strip().split('\n')

        for line in lines:
            line = line.strip()
            if line and not line.startswith('技能') and len(line) <= 20:
                # 清理可能的序号和特殊字符
                skill = line.replace('-', '').replace('•', '').replace('*', '').strip()
                if skill and skill not in skills:
                    skills.append(skill)

        return {"skills": skills[:8]}  # 限制最多8个技能

    def _get_fallback_skills(self, project_title: str) -> dict:
        """获取备用技能（当AI调用失败时）"""
        # 基于项目标题推测可能的技能
        fallback_skills = ["项目管理", "问题解决", "学习能力"]

        # 简单的关键词匹配
        title_lower = project_title.lower()
        if any(keyword in title_lower for keyword in ['python', 'django', 'flask']):
            fallback_skills.extend(["Python", "Web开发"])
        elif any(keyword in title_lower for keyword in ['javascript', 'react', 'vue', 'node']):
            fallback_skills.extend(["JavaScript", "前端开发"])
        elif any(keyword in title_lower for keyword in ['数据', 'data', '分析']):
            fallback_skills.extend(["数据分析", "统计分析"])
        elif any(keyword in title_lower for keyword in ['设计', 'design', 'ui', 'ux']):
            fallback_skills.extend(["设计思维", "用户体验"])

        return {"skills": fallback_skills[:6]}

    # ==================== 向量化功能 ====================

    def create_embedding(self, text: str) -> Optional[List[float]]:
        """
        创建文本的向量嵌入

        Args:
            text: 要向量化的文本

        Returns:
            List[float]: 向量嵌入，如果失败返回None
        """
        try:
            client = self._get_client()
            if not client:
                logger.warning("AI服务未配置，无法创建向量嵌入")
                return None

            # 调用OpenAI Embedding API
            response = client.embeddings.create(
                model=self.embedding_model,
                input=text
            )

            embedding = response.data[0].embedding
            logger.info(f"Created embedding for text (length: {len(text)})")
            return embedding

        except Exception as e:
            logger.error(f"Error creating embedding: {str(e)}")
            return None

    def get_text_hash(self, text: str) -> str:
        """
        计算文本的SHA-256哈希值

        Args:
            text: 要计算哈希的文本

        Returns:
            str: 哈希值
        """
        return hashlib.sha256(text.encode('utf-8')).hexdigest()

    def vectorize_growth_item(self, item: GrowthItem) -> bool:
        """
        向量化成长项（项目复盘）

        Args:
            item: 成长项实例

        Returns:
            bool: 是否成功
        """
        try:
            # 只处理已完成的项目
            if item.status != 'completed':
                logger.info(f"Skipping non-completed item: {item.item_id}")
                return False

            # 构建要向量化的文本
            text_parts = [item.title]
            if item.description:
                text_parts.append(item.description)
            if item.ai_summary:
                text_parts.append(item.ai_summary)

            text = " ".join(text_parts)
            text_hash = self.get_text_hash(text)

            # 检查是否已经存在相同的向量
            existing = VectorEmbedding.objects.filter(
                source_id=item.item_id,
                source_type='growth_item',
                source_text_hash=text_hash
            ).first()

            if existing:
                logger.info(f"Vector embedding already exists for item: {item.item_id}")
                return True

            # 创建向量嵌入
            embedding = self.create_embedding(text)
            if not embedding:
                return False

            # 删除旧的向量（如果存在）
            VectorEmbedding.objects.filter(
                source_id=item.item_id,
                source_type='growth_item'
            ).delete()

            # 保存新的向量
            VectorEmbedding.objects.create(
                user=item.user,
                source_id=item.item_id,
                source_type='growth_item',
                embedding=embedding,
                source_text_hash=text_hash
            )

            logger.info(f"Successfully vectorized growth item: {item.item_id}")
            return True

        except Exception as e:
            logger.error(f"Error vectorizing growth item {item.item_id}: {str(e)}")
            return False

    def vectorize_journal_entry(self, entry: JournalEntry, decrypted_content: str) -> bool:
        """
        向量化日记条目

        Args:
            entry: 日记条目实例
            decrypted_content: 解密后的日记内容（由前端提供）

        Returns:
            bool: 是否成功
        """
        try:
            # 构建要向量化的文本（只使用内容，不包含标题以保护隐私）
            text = decrypted_content
            text_hash = self.get_text_hash(text)

            # 检查是否已经存在相同的向量
            existing = VectorEmbedding.objects.filter(
                source_id=entry.entry_id,
                source_type='journal_entry',
                source_text_hash=text_hash
            ).first()

            if existing:
                logger.info(f"Vector embedding already exists for journal entry: {entry.entry_id}")
                return True

            # 创建向量嵌入
            embedding = self.create_embedding(text)
            if not embedding:
                return False

            # 删除旧的向量（如果存在）
            VectorEmbedding.objects.filter(
                source_id=entry.entry_id,
                source_type='journal_entry'
            ).delete()

            # 保存新的向量
            VectorEmbedding.objects.create(
                user=entry.user,
                source_id=entry.entry_id,
                source_type='journal_entry',
                embedding=embedding,
                source_text_hash=text_hash
            )

            logger.info(f"Successfully vectorized journal entry: {entry.entry_id}")
            return True

        except Exception as e:
            logger.error(f"Error vectorizing journal entry {entry.entry_id}: {str(e)}")
            return False

    def batch_vectorize_user_data(self, user_id: str) -> Dict[str, Any]:
        """
        批量向量化用户的所有数据

        Args:
            user_id: 用户ID

        Returns:
            dict: 处理结果统计
        """
        try:
            from apps.users.models import User
            user = User.objects.get(user_id=user_id)

            results = {
                'growth_items': {'success': 0, 'failed': 0, 'skipped': 0},
                'journal_entries': {'success': 0, 'failed': 0, 'skipped': 0},
                'total_processed': 0
            }

            # 处理已完成的成长项
            completed_items = GrowthItem.objects.filter(
                user=user,
                status='completed'
            )

            for item in completed_items:
                if self.vectorize_growth_item(item):
                    results['growth_items']['success'] += 1
                else:
                    results['growth_items']['failed'] += 1
                results['total_processed'] += 1

            # 注意：日记条目需要前端提供解密内容，这里只是占位
            # 实际的日记向量化需要通过API端点处理
            journal_entries = JournalEntry.objects.filter(user=user)
            results['journal_entries']['skipped'] = journal_entries.count()

            logger.info(f"Batch vectorization completed for user {user_id}: {results}")
            return results

        except Exception as e:
            logger.error(f"Error in batch vectorization for user {user_id}: {str(e)}")
            return {'error': str(e)}

    def search_similar_memories(self, user_id: str, query_text: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        搜索相似的记忆片段

        Args:
            user_id: 用户ID
            query_text: 查询文本
            limit: 返回结果数量限制

        Returns:
            List[Dict]: 相似的记忆片段列表
        """
        try:
            from apps.users.models import User
            user = User.objects.get(user_id=user_id)

            # 创建查询向量
            query_embedding = self.create_embedding(query_text)
            if not query_embedding:
                return []

            # 使用pgvector进行相似性搜索
            # 修复：正确使用pgvector的距离操作符和向量格式
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT embedding_id, source_id, source_type,
                           embedding <=> %s::vector AS similarity
                    FROM vector_embeddings
                    WHERE user_id = %s
                    ORDER BY embedding <=> %s::vector
                    LIMIT %s
                """, [query_embedding, user.user_id, query_embedding, limit])

                rows = cursor.fetchall()

            # 将查询结果转换为VectorEmbedding对象
            similar_embeddings = []
            for row in rows:
                embedding_id, source_id, source_type, similarity = row
                try:
                    embedding_obj = VectorEmbedding.objects.get(embedding_id=embedding_id)
                    embedding_obj.similarity = similarity  # 添加相似度属性
                    similar_embeddings.append(embedding_obj)
                except VectorEmbedding.DoesNotExist:
                    continue

            results = []
            for embedding in similar_embeddings:
                # 根据source_type获取原始数据
                if embedding.source_type == 'growth_item':
                    try:
                        item = GrowthItem.objects.get(item_id=embedding.source_id)
                        results.append({
                            'type': 'growth_item',
                            'id': str(item.item_id),
                            'title': item.title,
                            'description': item.description,
                            'ai_summary': item.ai_summary,
                            'completed_at': item.completed_at.isoformat() if item.completed_at else None,
                            'similarity': float(embedding.similarity) if hasattr(embedding, 'similarity') else 0.0
                        })
                    except GrowthItem.DoesNotExist:
                        continue
                elif embedding.source_type == 'journal_entry':
                    # 日记条目由于加密，只返回基本信息
                    try:
                        entry = JournalEntry.objects.get(entry_id=embedding.source_id)
                        results.append({
                            'type': 'journal_entry',
                            'id': str(entry.entry_id),
                            'date': entry.entry_date.isoformat(),
                            'created_at': entry.created_at.isoformat(),
                            'similarity': float(embedding.similarity) if hasattr(embedding, 'similarity') else 0.0,
                            'note': '日记内容已加密，需要前端解密后显示'
                        })
                    except JournalEntry.DoesNotExist:
                        continue

            logger.info(f"Found {len(results)} similar memories for user {user_id}")
            return results

        except Exception as e:
            logger.error(f"Error searching similar memories for user {user_id}: {str(e)}")
            return []


# 全局AI服务实例
ai_service = AIService()
