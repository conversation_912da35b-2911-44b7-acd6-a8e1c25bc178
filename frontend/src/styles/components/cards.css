/**
 * 卡片样式系统 - 统一主题设计
 *
 * 文件功能：
 *   定义所有卡片相关的样式，包括基础样式、变体和内容区域
 *
 * 设计原则：
 *   - 基础卡片提供通用样式
 *   - 变体卡片提供特殊场景样式
 *   - 避免重复定义，提高维护性
 *   - 支持亮色和暗色主题
 *
 * 变体说明：
 *   - .card: 默认卡片样式，适用于大多数场景
 *   - .card-clean: 简约风格卡片，用于轻量视觉场景
 *   - .card-aurora: 极光风格卡片，用于特殊展示场景
 *
 * 内容区域：
 *   - .card-header: 卡片头部区域
 *   - .card-body: 卡片主体内容区域
 *   - .card-footer: 卡片底部区域
 *
 * 使用方式：
 *   <div className="card">
 *     <div className="card-header">标题</div>
 *     <div className="card-body">内容</div>
 *     <div className="card-footer">操作</div>
 *   </div>
 *
 * 作者: Mentia前端团队
 * 创建时间: 2025-09-11
 * 版本: v1.0 - 从globals.css拆分独立
 */

/* ================================
   基础卡片样式
   ================================ */

.card {
  background: var(--color-surface);
  border-radius: 1rem;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-border-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  backdrop-filter: blur(10px);
  position: relative;
}

/* 卡片顶部装饰线 */
.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary-500), var(--color-secondary-500));
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* 卡片悬停效果 */
.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
  border-color: var(--color-primary-200);
}

.card:hover::before {
  opacity: 1;
}

/* ================================
   卡片变体样式
   ================================ */

/* 简约风格卡片 - 用于需要更轻量视觉的场景 */
.card-clean {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid rgb(229, 231, 235);
  transition: all 0.2s ease;
}

.card-clean:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-color: rgb(209, 213, 219);
}

/* 极光风格卡片 - 用于特殊展示场景 */
.card-aurora {
  border-radius: 1rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
}

.card-aurora:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transform: scale(1.02);
}

/* 注意：极光背景效果现在通过 .aurora-base 工具类提供 */
/* 使用方式：<div className="card card-aurora aurora-base"> */

/* ================================
   卡片内容区域样式
   ================================ */

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--color-border-light);
  background-color: var(--color-surface-elevated);
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--color-border-light);
  background-color: var(--color-surface-elevated);
}

/* ================================
   暗色主题适配
   ================================ */

.dark .card {
  background: rgba(30, 30, 30, 0.8);
  backdrop-filter: blur(16px) saturate(180%);
  -webkit-backdrop-filter: blur(16px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.dark .card:hover {
  background: rgba(35, 35, 35, 0.85);
  border-color: rgba(255, 255, 255, 0.15);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* ================================
   动画定义
   ================================ */

/* 注意：aurora动画现在在 utilities/effects.css 中定义 */
