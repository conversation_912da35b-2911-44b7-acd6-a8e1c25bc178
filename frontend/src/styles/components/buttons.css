/**
 * 按钮样式系统 - 心流体验设计
 *
 * 文件功能：
 *   定义所有按钮相关的样式，包括基础样式、变体和状态
 *
 * 设计原则：
 *   - 提供视觉反馈和交互动画
 *   - 支持多种变体和状态
 *   - 保持一致的视觉语言
 *   - 支持亮色和暗色主题
 *
 * 变体说明：
 *   - .btn: 基础按钮样式
 *   - .btn-primary: 主要操作按钮
 *   - .btn-secondary: 次要操作按钮
 *   - .btn-success: 成功状态按钮
 *   - .btn-warning: 警告状态按钮
 *   - .btn-error: 错误状态按钮
 *   - .btn-outline: 轮廓按钮
 *   - .btn-ghost: 幽灵按钮
 *
 * 使用方式：
 *   <button className="btn btn-primary">主要操作</button>
 *   <button className="btn btn-outline">次要操作</button>
 *
 * 作者: Mentia前端团队
 * 创建时间: 2025-09-11
 * 版本: v1.0 - 从globals.css拆分独立
 */

/* ================================
   基础按钮样式
   ================================ */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.75rem;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  outline: none;
  cursor: pointer;
  border: none;
  box-shadow: var(--shadow-sm);
  transform: translateY(0);
  position: relative;
  overflow: hidden;
}

/* 按钮波纹效果 */
.btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

/* ================================
   按钮交互状态
   ================================ */

.btn:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-lg);
}

.btn:hover::before {
  width: 300px;
  height: 300px;
}

.btn:active {
  transform: translateY(-1px) scale(1.01);
  box-shadow: var(--shadow-md);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.btn:disabled:hover {
  transform: none;
  box-shadow: var(--shadow-sm);
}

/* ================================
   按钮变体样式
   ================================ */

/* 主要按钮 */
.btn-primary {
  color: var(--color-text-inverse);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1), 0 0 0 1px var(--color-primary-500);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px var(--color-primary-600);
}

/* 注意：主要按钮背景现在通过 .gradient-primary 工具类提供 */
/* 使用方式：<button className="btn btn-primary gradient-primary"> */

.btn-primary:focus {
  box-shadow: 0 0 0 3px var(--color-primary-200);
}

/* 次要按钮 */
.btn-secondary {
  background-color: var(--color-neutral-100);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.btn-secondary:hover {
  background-color: var(--color-neutral-200);
  border-color: var(--color-neutral-300);
}

/* 成功按钮 */
.btn-success {
  color: var(--color-text-inverse);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1), 0 0 0 1px var(--color-success-500);
}

.btn-success:hover {
  background: linear-gradient(135deg, var(--color-success-600) 0%, var(--color-success-700) 100%);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px var(--color-success-600);
}

/* 警告按钮 */
.btn-warning {
  color: var(--color-text-inverse);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1), 0 0 0 1px var(--color-warning-500);
}

.btn-warning:hover {
  background: linear-gradient(135deg, var(--color-warning-600) 0%, var(--color-warning-700) 100%);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px var(--color-warning-600);
}

/* 错误按钮 */
.btn-error {
  color: var(--color-text-inverse);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1), 0 0 0 1px var(--color-error-500);
}

.btn-error:hover {
  background: linear-gradient(135deg, var(--color-error-600) 0%, var(--color-error-700) 100%);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px var(--color-error-600);
}

/* 注意：按钮背景渐变现在通过工具类提供 */
/* 使用方式：
   <button className="btn btn-success gradient-success">成功</button>
   <button className="btn btn-warning gradient-warning">警告</button>
   <button className="btn btn-error gradient-error">错误</button>
*/

/* 轮廓按钮 */
.btn-outline {
  background-color: transparent;
  color: var(--color-primary-600);
  border: 1px solid var(--color-primary-300);
}

.btn-outline:hover {
  background-color: var(--color-primary-50);
  border-color: var(--color-primary-400);
  color: var(--color-primary-700);
}

/* 幽灵按钮 */
.btn-ghost {
  background-color: transparent;
  color: var(--color-text-secondary);
  border: none;
}

.btn-ghost:hover {
  background-color: var(--color-neutral-100);
  color: var(--color-text-primary);
}

/* ================================
   暗色主题适配
   ================================ */

.dark .btn {
  background: rgba(40, 40, 40, 0.8);
  color: var(--color-text-primary);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .btn:hover {
  background: rgba(50, 50, 50, 0.9);
  border-color: rgba(255, 255, 255, 0.15);
}

.dark .btn-primary {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));
  color: white;
  border: 1px solid var(--color-primary-500);
}

.dark .btn-primary:hover {
  background: linear-gradient(135deg, var(--color-primary-700), var(--color-primary-800));
  border-color: var(--color-primary-400);
}
