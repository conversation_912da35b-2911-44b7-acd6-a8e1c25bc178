/**
 * 输入框样式系统 - 统一主题设计
 *
 * 文件功能：
 *   定义所有输入框相关的样式，包括基础样式、状态和特效
 *
 * 设计原则：
 *   - 提供清晰的视觉反馈
 *   - 支持多种状态（正常、聚焦、错误）
 *   - 保持一致的交互体验
 *   - 支持亮色和暗色主题
 *
 * 样式说明：
 *   - .input: 基础输入框样式
 *   - .input-error: 错误状态输入框
 *   - .input-glass: 玻璃效果输入框
 *
 * 状态说明：
 *   - 默认状态: 中性边框和背景
 *   - 悬停状态: 边框颜色加深
 *   - 聚焦状态: 主题色边框和阴影
 *   - 错误状态: 错误色边框和背景
 *
 * 使用方式：
 *   <input className="input" placeholder="请输入内容" />
 *   <input className="input input-error" placeholder="错误状态" />
 *   <input className="input input-glass" placeholder="玻璃效果" />
 *
 * 作者: Mentia前端团队
 * 创建时间: 2025-09-11
 * 版本: v1.0 - 从globals.css拆分独立
 */

/* ================================
   基础输入框样式
   ================================ */

.input {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  box-shadow: var(--shadow-sm);
  font-size: 0.875rem;
  background-color: var(--color-surface);
  color: var(--color-text-primary);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 占位符样式 */
.input::placeholder {
  color: var(--color-text-muted);
}

/* ================================
   输入框交互状态
   ================================ */

/* 聚焦状态 */
.input:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px var(--color-primary-100);
  background-color: var(--color-surface);
}

/* 悬停状态（非聚焦时） */
.input:hover:not(:focus) {
  border-color: var(--color-neutral-300);
}

/* ================================
   输入框状态变体
   ================================ */

/* 错误状态 */
.input-error {
  border-color: var(--color-error-500);
  background-color: var(--color-error-50);
}

.input-error:focus {
  outline: none;
  border-color: var(--color-error-500);
  box-shadow: 0 0 0 3px var(--color-error-100);
}

/* 玻璃效果输入框 */
.input-glass:focus {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(20, 184, 166, 0.5);
}

/* 注意：玻璃效果现在通过工具类提供 */
/* 使用方式：<input className="input input-glass glass-base glass-light glass-blur-light"> */

/* ================================
   暗色主题适配
   ================================ */

.dark .input {
  background: rgba(30, 30, 30, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--color-text-primary);
}

.dark .input:focus {
  background: rgba(35, 35, 35, 0.9);
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgba(var(--color-primary-500-rgb), 0.1);
}

.dark .input:hover:not(:focus) {
  border-color: rgba(255, 255, 255, 0.15);
}

.dark .input-error {
  background: rgba(var(--color-error-500-rgb), 0.1);
  border-color: var(--color-error-500);
}

.dark .input-error:focus {
  background: rgba(var(--color-error-500-rgb), 0.15);
  border-color: var(--color-error-500);
  box-shadow: 0 0 0 3px rgba(var(--color-error-500-rgb), 0.1);
}

.dark .input-glass {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .input-glass:focus {
  background: rgba(0, 0, 0, 0.4);
  border-color: rgba(20, 184, 166, 0.5);
}
