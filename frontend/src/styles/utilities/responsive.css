/**
 * 响应式工具类系统
 *
 * 文件功能：
 *   定义响应式断点和工具类，提供统一的移动端适配方案
 *
 * 设计原则：
 *   - 移动优先：默认样式针对移动端设计
 *   - 渐进增强：通过断点逐步增强桌面端体验
 *   - 语义化断点：使用有意义的断点名称
 *   - 灵活组合：支持多种响应式组合
 *
 * 断点系统：
 *   - mobile: 默认 (0px+)
 *   - tablet: 768px+
 *   - desktop: 1024px+
 *   - wide: 1280px+
 *   - ultra: 1536px+
 *
 * 工具类分类：
 *   - 布局响应式 (.responsive-*)
 *   - 间距响应式 (.spacing-*)
 *   - 字体响应式 (.text-*)
 *   - 显示响应式 (.show-*, .hide-*)
 *
 * 作者: Mentia前端团队
 * 创建时间: 2025-09-11
 * 版本: v1.0 - 响应式系统优化
 */

/* ================================
   响应式断点定义
   ================================ */

/* 移动端优先的断点系统 */
:root {
  --breakpoint-tablet: 768px;
  --breakpoint-desktop: 1024px;
  --breakpoint-wide: 1280px;
  --breakpoint-ultra: 1536px;
}

/* ================================
   容器响应式系统
   ================================ */

/* 响应式容器 */
.container-responsive {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-md);
  padding-right: var(--spacing-md);
}

@media (min-width: 768px) {
  .container-responsive {
    max-width: 768px;
    padding-left: var(--spacing-lg);
    padding-right: var(--spacing-lg);
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    max-width: 1024px;
    padding-left: var(--spacing-xl);
    padding-right: var(--spacing-xl);
  }
}

@media (min-width: 1280px) {
  .container-responsive {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container-responsive {
    max-width: 1536px;
  }
}

/* ================================
   网格响应式系统
   ================================ */

/* 响应式网格 */
.grid-responsive {
  display: grid;
  gap: var(--spacing-md);
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .grid-responsive {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
  }
}

@media (min-width: 1024px) {
  .grid-responsive {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xl);
  }
}

@media (min-width: 1280px) {
  .grid-responsive {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 卡片网格 */
.grid-cards {
  display: grid;
  gap: var(--spacing-lg);
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .grid-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-cards {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* ================================
   间距响应式系统
   ================================ */

/* 响应式内边距 */
.padding-responsive {
  padding: var(--spacing-md);
}

@media (min-width: 768px) {
  .padding-responsive {
    padding: var(--spacing-lg);
  }
}

@media (min-width: 1024px) {
  .padding-responsive {
    padding: var(--spacing-xl);
  }
}

/* 响应式外边距 */
.margin-responsive {
  margin: var(--spacing-md);
}

@media (min-width: 768px) {
  .margin-responsive {
    margin: var(--spacing-lg);
  }
}

@media (min-width: 1024px) {
  .margin-responsive {
    margin: var(--spacing-xl);
  }
}

/* ================================
   字体响应式系统
   ================================ */

/* 响应式标题 */
.text-heading-responsive {
  font-size: var(--font-size-2xl);
  line-height: var(--line-height-tight);
  font-weight: var(--font-weight-bold);
}

@media (min-width: 768px) {
  .text-heading-responsive {
    font-size: var(--font-size-3xl);
  }
}

@media (min-width: 1024px) {
  .text-heading-responsive {
    font-size: var(--font-size-4xl);
  }
}

/* 响应式副标题 */
.text-subheading-responsive {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-normal);
  font-weight: var(--font-weight-semibold);
}

@media (min-width: 768px) {
  .text-subheading-responsive {
    font-size: var(--font-size-xl);
  }
}

@media (min-width: 1024px) {
  .text-subheading-responsive {
    font-size: var(--font-size-2xl);
  }
}

/* 响应式正文 */
.text-body-responsive {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
}

@media (min-width: 768px) {
  .text-body-responsive {
    font-size: var(--font-size-base);
  }
}

/* ================================
   显示响应式系统
   ================================ */

/* 移动端显示/隐藏 */
.show-mobile {
  display: block;
}

.hide-mobile {
  display: none;
}

@media (min-width: 768px) {
  .show-mobile {
    display: none;
  }
  
  .hide-mobile {
    display: block;
  }
}

/* 平板端显示/隐藏 */
.show-tablet {
  display: none;
}

.hide-tablet {
  display: block;
}

@media (min-width: 768px) {
  .show-tablet {
    display: block;
  }
  
  .hide-tablet {
    display: none;
  }
}

@media (min-width: 1024px) {
  .show-tablet {
    display: none;
  }
  
  .hide-tablet {
    display: block;
  }
}

/* 桌面端显示/隐藏 */
.show-desktop {
  display: none;
}

.hide-desktop {
  display: block;
}

@media (min-width: 1024px) {
  .show-desktop {
    display: block;
  }
  
  .hide-desktop {
    display: none;
  }
}

/* ================================
   布局响应式系统
   ================================ */

/* 响应式弹性布局 */
.flex-responsive {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

@media (min-width: 768px) {
  .flex-responsive {
    flex-direction: row;
    gap: var(--spacing-lg);
  }
}

/* 响应式侧边栏布局 */
.layout-sidebar {
  display: flex;
  flex-direction: column;
}

@media (min-width: 1024px) {
  .layout-sidebar {
    flex-direction: row;
  }
  
  .layout-sidebar .sidebar {
    width: 256px;
    flex-shrink: 0;
  }
  
  .layout-sidebar .main {
    flex: 1;
    min-width: 0;
  }
}

/* ================================
   组件响应式适配
   ================================ */

/* 响应式按钮 */
.btn-responsive {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
}

@media (min-width: 768px) {
  .btn-responsive {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-base);
  }
}

/* 响应式卡片 */
.card-responsive {
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

@media (min-width: 768px) {
  .card-responsive {
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
  }
}

@media (min-width: 1024px) {
  .card-responsive {
    padding: var(--spacing-xl);
  }
}
