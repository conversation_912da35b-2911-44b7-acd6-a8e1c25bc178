/**
 * 特效工具类 - 统一的视觉效果系统
 *
 * 文件功能：
 *   定义可复用的视觉效果，包括极光、玻璃、渐变等特效
 *
 * 设计原则：
 *   - 基础效果类提供核心功能
 *   - 组件可以组合使用多个效果
 *   - 避免重复定义相同的效果
 *   - 支持亮色和暗色主题
 *
 * 效果分类：
 *   - 极光效果 (.aurora-*)
 *   - 玻璃效果 (.glass-*)
 *   - 渐变效果 (.gradient-*)
 *   - 阴影效果 (.shadow-*)
 *
 * 使用方式：
 *   <div className="card aurora-base">极光卡片</div>
 *   <div className="input glass-light">玻璃输入框</div>
 *
 * 作者: Mentia前端团队
 * 创建时间: 2025-09-11
 * 版本: v1.0 - 样式重复清理优化
 */

/* ================================
   极光效果系统
   ================================ */

/* 基础极光效果 - 所有极光组件的基础 */
.aurora-base {
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(12px);
}

.aurora-base::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(20, 184, 166, 0.05) 0%,
    rgba(59, 130, 246, 0.05) 25%,
    rgba(147, 51, 234, 0.05) 50%,
    rgba(236, 72, 153, 0.05) 75%,
    rgba(251, 191, 36, 0.05) 100%);
  background-size: 400% 400%;
  animation: aurora 20s ease infinite;
  z-index: -1;
}

/* 极光强度变体 */
.aurora-light::before {
  background: linear-gradient(135deg,
    rgba(20, 184, 166, 0.03) 0%,
    rgba(59, 130, 246, 0.03) 25%,
    rgba(147, 51, 234, 0.03) 50%,
    rgba(236, 72, 153, 0.03) 75%,
    rgba(251, 191, 36, 0.03) 100%);
}

.aurora-medium::before {
  background: linear-gradient(135deg,
    rgba(20, 184, 166, 0.08) 0%,
    rgba(59, 130, 246, 0.08) 25%,
    rgba(147, 51, 234, 0.08) 50%,
    rgba(236, 72, 153, 0.08) 75%,
    rgba(251, 191, 36, 0.08) 100%);
}

.aurora-strong::before {
  background: linear-gradient(135deg,
    rgba(20, 184, 166, 0.12) 0%,
    rgba(59, 130, 246, 0.12) 25%,
    rgba(147, 51, 234, 0.12) 50%,
    rgba(236, 72, 153, 0.12) 75%,
    rgba(251, 191, 36, 0.12) 100%);
}

/* ================================
   玻璃效果系统
   ================================ */

/* 基础玻璃效果 */
.glass-base {
  backdrop-filter: blur(16px) saturate(180%);
  -webkit-backdrop-filter: blur(16px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 玻璃透明度变体 */
.glass-light {
  background: rgba(255, 255, 255, 0.1);
}

.glass-medium {
  background: rgba(255, 255, 255, 0.15);
}

.glass-heavy {
  background: rgba(255, 255, 255, 0.25);
}

/* 玻璃模糊强度变体 */
.glass-blur-light {
  backdrop-filter: blur(8px) saturate(150%);
  -webkit-backdrop-filter: blur(8px) saturate(150%);
}

.glass-blur-medium {
  backdrop-filter: blur(16px) saturate(180%);
  -webkit-backdrop-filter: blur(16px) saturate(180%);
}

.glass-blur-heavy {
  backdrop-filter: blur(24px) saturate(200%);
  -webkit-backdrop-filter: blur(24px) saturate(200%);
}

/* ================================
   渐变效果系统
   ================================ */

/* 主题色渐变 */
.gradient-primary {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--color-secondary-500), var(--color-secondary-600));
}

.gradient-success {
  background: linear-gradient(135deg, var(--color-success-500), var(--color-success-600));
}

.gradient-warning {
  background: linear-gradient(135deg, var(--color-warning-500), var(--color-warning-600));
}

.gradient-error {
  background: linear-gradient(135deg, var(--color-error-500), var(--color-error-600));
}

/* 文本渐变 */
.gradient-text-primary {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-secondary-600));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* ================================
   阴影效果系统
   ================================ */

.shadow-soft {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-glow-primary {
  box-shadow: 0 0 20px rgba(var(--color-primary-500-rgb), 0.3);
}

.shadow-glow-success {
  box-shadow: 0 0 20px rgba(var(--color-success-500-rgb), 0.3);
}

.shadow-glow-error {
  box-shadow: 0 0 20px rgba(var(--color-error-500-rgb), 0.3);
}

/* ================================
   暗色主题适配
   ================================ */

.dark .glass-light {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .glass-medium {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.dark .glass-heavy {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .aurora-base::before {
  background: linear-gradient(135deg,
    rgba(20, 184, 166, 0.08) 0%,
    rgba(59, 130, 246, 0.08) 25%,
    rgba(147, 51, 234, 0.08) 50%,
    rgba(236, 72, 153, 0.08) 75%,
    rgba(251, 191, 36, 0.08) 100%);
}

/* ================================
   动画定义
   ================================ */

@keyframes aurora {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* 脉冲动画 */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(var(--color-primary-500-rgb), 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(var(--color-primary-500-rgb), 0.8);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}
